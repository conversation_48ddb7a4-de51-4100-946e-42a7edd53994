'use client'

import { useState, useEffect } from 'react'
import { SmartTypography } from './SmartTypography'
import Link from 'next/link'
import Image from 'next/image'
import { Day1Badge } from './Day1Badge'

interface ImmersiveReaderProps {
  entry: {
    id: string
    title: string
    body_md: string
    writer_name: string
    writer_id: string
    created_at: string
    is_free: boolean
    is_hidden: boolean
    love_count?: number
    writer_price?: number // Price in cents for subscription
  }
  user: { id: string; role: string } | null
  canReadFull: boolean
  children: React.ReactNode // For other components like comments, love button, etc.
  writerProfile?: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
    has_day1_badge?: boolean
    signup_number?: number
  } | null
}

export function ImmersiveReader({ entry, user, canReadFull, children, writerProfile }: ImmersiveReaderProps) {
  const [darkMode, setDarkMode] = useState(true) // Default to dark mode
  const [focusMode, setFocusMode] = useState(false)

  // Keep dark mode as default - no auto-detection needed since it's the default
  useEffect(() => {
    // Dark mode is now the default reading experience
    // Users can manually switch to light mode if preferred
  }, [])

  const formatDateTime = (dateString: string) => {
    // Use consistent format to avoid hydration issues
    const date = new Date(dateString)
    const year = date.getFullYear()
    const month = date.toLocaleDateString('en-US', { month: 'long' })
    const day = date.getDate()

    return `${month} ${day}, ${year}`
  }

  const getTeaserText = (content: string) => {
    const words = content.split(' ')
    return words.slice(0, 50).join(' ') + (words.length > 50 ? '...' : '')
  }

  return (
    <div className={`min-h-screen transition-all duration-1000 ${
      darkMode
        ? 'bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white'
        : 'bg-gradient-to-br from-slate-50 via-blue-50/20 to-purple-50/20 text-gray-900'
    }`}>
      
      {/* Reading Controls - Bottom right on mobile, top left on desktop */}
      <div className={`fixed z-40 flex gap-2 transition-all duration-500 ${
        focusMode ? 'opacity-20 hover:opacity-100' : 'opacity-100'
      } ${
        // Mobile: bottom right, Desktop: top left below nav
        'bottom-4 right-4 md:top-20 md:left-4 md:bottom-auto md:right-auto md:flex-col'
      }`}>
        {/* Dark Mode Toggle */}
        <button
          onClick={() => setDarkMode(!darkMode)}
          className={`flex items-center justify-center w-8 h-8 md:w-10 md:h-10 rounded-full text-sm md:text-lg transition-all duration-500 ${
            darkMode
              ? 'text-white bg-slate-700/70 hover:bg-slate-600/70 border border-slate-600/50'
              : 'text-gray-700 bg-white/90 hover:bg-white border border-gray-200 shadow-sm'
          }`}
          title={darkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
        >
          {darkMode ? '☀️' : '🌙'}
        </button>

        {/* Focus Mode Toggle */}
        <button
          onClick={() => setFocusMode(!focusMode)}
          className={`flex items-center justify-center w-8 h-8 md:w-10 md:h-10 rounded-full text-sm md:text-lg transition-all duration-500 ${
            darkMode
              ? 'text-white bg-slate-700/70 hover:bg-slate-600/70 border border-slate-600/50'
              : 'text-gray-700 bg-white/90 hover:bg-white border border-gray-200 shadow-sm'
          }`}
          title={focusMode ? 'Exit Focus Mode' : 'Enter Focus Mode'}
        >
          {focusMode ? '👁️' : '🎯'}
        </button>
      </div>

      <div className={`max-w-4xl mx-auto px-4 sm:px-6 py-6 sm:py-8 transition-all duration-700 ${
        focusMode ? 'max-w-3xl py-12' : ''
      }`}>
        
        {/* Back Navigation */}
        <div className={`transition-all duration-500 ${
          focusMode ? 'opacity-30 hover:opacity-100' : 'opacity-100'
        }`}>
          <Link
            href={entry.is_hidden && user?.id === entry.writer_id ? '/dashboard' : `/u/${entry.writer_id}`}
            className={`inline-flex items-center mb-6 sm:mb-8 font-serif text-sm sm:text-base px-4 py-2 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 ${
              darkMode
                ? 'text-white/80 hover:text-white bg-slate-800/50 hover:bg-slate-700/50 border border-slate-700/30'
                : 'text-gray-600 hover:text-gray-800 bg-white hover:bg-gray-50 border border-gray-200'
            }`}
          >
            ← {entry.is_hidden && user?.id === entry.writer_id ? 'Back to Dashboard' : `Back to ${entry.writer_name}'s Profile`}
          </Link>
        </div>

        {/* Enhanced Entry Content */}
        <article className={`rounded-2xl p-6 sm:p-8 lg:p-12 shadow-xl mb-8 transition-all duration-1000 ${
          darkMode
            ? 'bg-slate-800/50 backdrop-blur-xl border border-slate-700/30'
            : 'bg-white/90 backdrop-blur-sm border border-gray-100'
        } ${focusMode ? 'shadow-2xl' : 'shadow-xl'}`}>
          
          {/* Entry Header */}
          <header className={`mb-8 sm:mb-10 transition-all duration-500 ${
            focusMode ? 'opacity-70 hover:opacity-100' : 'opacity-100'
          }`}>
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-serif mb-6 leading-tight transition-all duration-500 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              {entry.title}
            </h1>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
              <div className="flex items-center gap-3">
                <div>
                  {writerProfile?.profile_picture_url || writerProfile?.avatar ? (
                    <Image
                      src={writerProfile.profile_picture_url || writerProfile.avatar || ''}
                      alt={entry.writer_name}
                      width={40}
                      height={40}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-lg">{entry.writer_name[0].toUpperCase()}</span>
                    </div>
                  )}
                </div>
                <div>
                  <div className={`font-medium flex items-center gap-2 ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                    <span>by {entry.writer_name}</span>
                    {writerProfile?.has_day1_badge && (
                      <Day1Badge
                        signupNumber={writerProfile.signup_number}
                        className="scale-75"
                      />
                    )}
                  </div>
                  <div className={`text-sm ${darkMode ? 'text-white/60' : 'text-gray-500'}`}>{formatDateTime(entry.created_at)}</div>
                </div>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <span className={darkMode ? 'text-white/60' : 'text-gray-500'}>📖 {Math.ceil(entry.body_md.split(' ').length / 200)} min read</span>
              </div>
            </div>
          </header>

          {/* Entry Body with Smart Typography */}
          <div className="prose prose-lg max-w-none">
            <div className={`mb-6 flex flex-wrap gap-2 transition-all duration-500 ${
              focusMode ? 'opacity-50 hover:opacity-100' : 'opacity-100'
            }`}>
              {entry.is_free && (
                <span className={`text-sm font-bold px-3 py-2 rounded-full border transition-all duration-500 ${
                  darkMode
                    ? 'text-green-400 bg-green-900/30 border-green-800/50'
                    : 'text-green-700 bg-green-100 border-green-200'
                }`}>
                  ✨ FREE ENTRY
                </span>
              )}
              {entry.is_hidden && (
                <span className={`text-sm font-bold px-3 py-2 rounded-full border transition-all duration-500 ${
                  darkMode
                    ? 'text-orange-400 bg-orange-900/30 border-orange-800/50'
                    : 'text-orange-700 bg-orange-100 border-orange-200'
                }`}>
                  🔒 PRIVATE - Only visible to you
                </span>
              )}
            </div>
            
            {/* Smart Typography Rendering */}
            {canReadFull ? (
              <SmartTypography
                content={entry.body_md}
                className={darkMode ? 'text-white' : 'text-gray-700'}
                zenMode={darkMode}
                isDesktop={typeof window !== 'undefined' && window.innerWidth >= 1024}
                enableAdvancedFeatures={true}
              />
            ) : (
              <div>
                {/* Teaser Content */}
                <div className={`font-serif leading-relaxed mb-6 ${darkMode ? 'text-white' : 'text-gray-700'}`}>
                  {getTeaserText(entry.body_md)}
                </div>

                {/* Paywall Call-to-Action */}
                <div className={`border rounded-lg p-6 text-center transition-all duration-500 ${
                  darkMode
                    ? 'bg-slate-700/50 border-slate-600/50'
                    : 'bg-gray-50 border-gray-200'
                }`}>
                  <div className="mb-4">
                    <h3 className={`text-lg font-medium mb-2 ${
                      darkMode ? 'text-white' : 'text-gray-900'
                    }`}>
                      Continue reading {entry.writer_name}'s story
                    </h3>
                    <p className={`text-sm ${
                      darkMode ? 'text-white/70' : 'text-gray-600'
                    }`}>
                      This is a premium entry. Subscribe to read the full story.
                    </p>
                  </div>

                  <button
                    onClick={async () => {
                      try {
                        const response = await fetch('/api/subscribe', {
                          method: 'POST',
                          headers: { 'Content-Type': 'application/json' },
                          body: JSON.stringify({ writerId: entry.writer_id }),
                        })
                        const data = await response.json()
                        if (data.url) {
                          window.location.href = data.url
                        } else if (data.success) {
                          // Free follow successful
                          window.location.reload()
                        } else {
                          alert(data.error || 'Failed to subscribe')
                        }
                      } catch {
                        alert('Failed to subscribe')
                      }
                    }}
                    className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                      darkMode
                        ? 'bg-white text-slate-900 hover:bg-gray-100'
                        : 'bg-gray-800 text-white hover:bg-gray-700'
                    }`}
                  >
                    {entry.writer_price && entry.writer_price > 0
                      ? `Subscribe to ${entry.writer_name} - $${(entry.writer_price / 100).toFixed(2)}/month`
                      : `Subscribe to ${entry.writer_name}`
                    }
                  </button>
                </div>
              </div>
            )}
          </div>
        </article>

        {/* Other components (love button, comments, etc.) */}
        <div className={`transition-all duration-500 ${
          focusMode ? 'opacity-60 hover:opacity-100' : 'opacity-100'
        }`}>
          {children}
        </div>
      </div>
    </div>
  )
}
